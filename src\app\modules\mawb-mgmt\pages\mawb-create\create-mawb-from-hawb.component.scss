.width-full {
	width: 100%;
}

.gutters-x-2 {
	--bs-gutter-x: 0.5rem;
}

.gutters-x-3 {
	--bs-gutter-x: 1rem;
}

.gutters-x-4 {
	--bs-gutter-x: 2rem;
}

.row {
	margin-top: calc(var(--bs-gutter-y) * -1);
	margin-right: calc(var(--bs-gutter-x) * -0.5);
	margin-left: calc(var(--bs-gutter-x) * -0.5);
	margin-bottom: 10px;
}

.row > * {
	flex-shrink: 0;
	width: 100%;
	max-width: 100%;
	padding-right: calc(var(--bs-gutter-x) * 0.5);
	padding-left: calc(var(--bs-gutter-x) * 0.5);
	margin-top: var(--bs-gutter-y);
}

.orll-sli-also-notify {
	&__box {
		min-height: 76px;
	}

	&__panel {
		margin-bottom: 20px;
	}

	&__title {
		color: var(--iata-blue-primary);
		font-size: 32px;
		margin-bottom: 0;
	}

	&__add-button {
		color: var(--iata-blue-primary) !important;
		border: 1px solid var(--iata-blue-primary);
	}

	::ng-deep .mat-expansion-panel-header-description {
		flex-grow: 0 !important;
	}
}

.piece-rcp-container {
	margin-bottom: 20px;
}

.piece-rcp-container > .row > .col {
	flex: 0 0 calc((100% - 450px) / 6);
	width: calc((100% - 450px) / 6);
}

.col-nature-and-quantity-of-goods {
	flex: 0 0 400px !important;
	width: 400px;
}

.no-label-form-header {
	border-bottom: 1px solid var(--iata-grey-100);
}

.no-label-form {
	padding-top: 16px;
}

.no-label-form mat-form-field {
	width: 100%;
}

.rate-charge {
	margin-right: calc(var(--bs-gutter-x) * 1.5);
}

.ml-auto {
	margin-left: auto;
}

.cancel-button {
	margin-right: 20px;
	color: var(--iata-blue-primary) !important;
	border: 1px solid var(--iata-blue-primary);
}

.preview-button {
	display: inline-flex;
	align-items: center;
	margin-right: 20px;
	box-shadow: 0 6px 16px 0 rgba(56, 86, 235, 0.24);
	color: var(--iata-blue-primary) !important;
	border: 1px solid var(--iata-blue-primary);
}

.unit {
	margin-right: 5px;
	font-size: 14px;
	color: var(--iata-black);
}

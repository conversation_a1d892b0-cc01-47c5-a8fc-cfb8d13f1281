import { Component, DestroyRef, OnInit } from '@angular/core';
import { TranslatePipe } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { FormControl, NonNullableFormBuilder, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { combineLatest, startWith } from 'rxjs';

@Component({
	selector: 'orll-prepaid-collect',
	imports: [CommonModule, ReactiveFormsModule, TranslatePipe, MatFormFieldModule, MatInput],
	templateUrl: './prepaid-collect.component.html',
	styleUrl: './prepaid-collect.component.scss',
})
export class PrepaidCollectComponent implements OnInit {
	prepaidForm = this.fb.group({
		weightChargePrepaid: new FormControl<number | null>({ value: null, disabled: true }),
		weightChargeCollect: new FormControl<number | null>({ value: null, disabled: true }),
		valuationChargePrepaid: new FormControl<number | null>({ value: null, disabled: true }),
		valuationChargeCollect: new FormControl<number | null>({ value: null, disabled: true }),
		taxPrepaid: new FormControl<number | null>({ value: null, disabled: true }),
		taxCollect: new FormControl<number | null>({ value: null, disabled: true }),
		totalOtherChargesDueAgentPrepaid: new FormControl<number | null>({ value: null, disabled: true }),
		totalOtherChargesDueAgentCollect: new FormControl<number | null>({ value: null, disabled: true }),
		totalOtherChargesDueCarrierPrepaid: new FormControl<number | null>({ value: null, disabled: true }),
		totalOtherChargesDueCarrierCollect: new FormControl<number | null>({ value: null, disabled: true }),
		totalPrepaid: new FormControl<number | null>({ value: null, disabled: true }),
		totalCollect: new FormControl<number | null>({ value: null, disabled: true }),
	});

	constructor(
		private readonly fb: NonNullableFormBuilder,
		private readonly destroyRef: DestroyRef
	) {}

	ngOnInit(): void {
		combineLatest([
			this.prepaidForm.get('weightChargePrepaid')!.valueChanges.pipe(startWith(null), takeUntilDestroyed(this.destroyRef)),
			this.prepaidForm.get('valuationChargePrepaid')!.valueChanges.pipe(startWith(null), takeUntilDestroyed(this.destroyRef)),
			this.prepaidForm.get('taxPrepaid')!.valueChanges.pipe(startWith(null), takeUntilDestroyed(this.destroyRef)),
			this.prepaidForm
				.get('totalOtherChargesDueAgentPrepaid')!
				.valueChanges.pipe(startWith(null), takeUntilDestroyed(this.destroyRef)),
			this.prepaidForm
				.get('totalOtherChargesDueCarrierPrepaid')!
				.valueChanges.pipe(startWith(null), takeUntilDestroyed(this.destroyRef)),
		])
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe(([weightCharge, valuationCharge, taxCharge, agentCharge, carrierCharge]) => {
				this.prepaidForm.patchValue({
					totalPrepaid: Number(
						(
							(weightCharge ?? 0) +
							(valuationCharge ?? 0) +
							(taxCharge ?? 0) +
							(agentCharge ?? 0) +
							(carrierCharge ?? 0)
						).toFixed(2)
					),
				});
			});

		combineLatest([
			this.prepaidForm.get('weightChargeCollect')!.valueChanges.pipe(startWith(null), takeUntilDestroyed(this.destroyRef)),
			this.prepaidForm.get('valuationChargeCollect')!.valueChanges.pipe(startWith(null), takeUntilDestroyed(this.destroyRef)),
			this.prepaidForm.get('taxCollect')!.valueChanges.pipe(startWith(null), takeUntilDestroyed(this.destroyRef)),
			this.prepaidForm
				.get('totalOtherChargesDueAgentCollect')!
				.valueChanges.pipe(startWith(null), takeUntilDestroyed(this.destroyRef)),
			this.prepaidForm
				.get('totalOtherChargesDueCarrierCollect')!
				.valueChanges.pipe(startWith(null), takeUntilDestroyed(this.destroyRef)),
		])
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe(([weightCharge, valuationCharge, taxCharge, agentCharge, carrierCharge]) => {
				this.prepaidForm.patchValue({
					totalCollect: Number(
						(
							(weightCharge ?? 0) +
							(valuationCharge ?? 0) +
							(taxCharge ?? 0) +
							(agentCharge ?? 0) +
							(carrierCharge ?? 0)
						).toFixed(2)
					),
				});
			});
	}
}

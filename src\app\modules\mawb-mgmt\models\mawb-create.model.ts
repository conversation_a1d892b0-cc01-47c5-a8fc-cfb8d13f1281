export interface HawbCreateDto {
	id?: Id;
	sliId?: string;
	orgId: string;
	waybillPrefix: string;
	waybillNumber: string;
	partyList: PartyList[];
	sliPartyList?: PartyList[];
	accountingInformation: string;
	departureLocation: string;
	arrivalLocation: string;
	insuredAmount: Currency;
	weightValuationIndicator: string;
	otherChargesIndicator: string;
	declaredValueForCarriage: Currency;
	declaredValueForCustoms: Currency;
	textualHandlingInstructions: string;
	totalGrossWeight: number | null;
	rateClassCode: string | null;
	totalVolumetricWeight: number;
	rateCharge: Currency;
	goodsDescriptionForRate: string;
	otherChargeList: OtherChargeList[];
	carrierDeclarationDate: string;
	carrierDeclarationPlace: string;
	consignorDeclarationSignature: string;
	carrierDeclarationSignature: string;
}

export interface Id {
	iri: boolean;
	namespace: string;
	localName: string;
	resource: boolean;
	bnode: boolean;
	triple: boolean;
	literal: boolean;
}

export interface PartyList {
	id?: string | null;
	companyName: string;
	contactName: string;
	countryCode: string;
	regionCode: string;
	locationName: string;
	cityCode: string;
	textualPostCode: string;
	phoneNumber: string;
	emailAddress: string;
	companyType: string;
	iataCargoAgentCode: string;
}

export interface OtherChargeList {
	id?: string | null;
	chargePaymentType: string;
	entitlement: string;
	otherChargeAmount?: Currency;
	otherChargeCode: string;
	chargeQuantity?: number;
	locationIndicator?: string;
	reasonDescription?: string;
}

export interface Currency {
	currencyUnit: string;
	numericalValue: number | null;
}

import { Component, DestroyRef, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, NonNullableFormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { CurrencyInputComponent } from '@shared/components/currency-input/currency-input.component';
import { MatSelectChange, MatSelectModule } from '@angular/material/select';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { CodeName } from '@shared/models/code-name.model';
import { SliCreateRequestService } from '../../../../sli-mgmt/services/sli-create-request.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { startWith } from 'rxjs';

@Component({
	selector: 'orll-airport-info',
	imports: [
		ReactiveFormsModule,
		MatFormFieldModule,
		MatInputModule,
		TranslateModule,
		CurrencyInputComponent,
		MatSelectModule,
		MatAutocompleteModule,
	],
	templateUrl: './airport-info.component.html',
	styleUrl: './airport-info.component.scss',
})
export class AirportInfoComponent implements OnInit {
	@Input()
	currencies: string[] = [];

	@Output()
	wtOrValChange = new EventEmitter<MatSelectChange>();

	airportInfoForm = this.fb.group({
		departureAndRequestedRouting: ['', [Validators.required]],
		airportOfDestination: ['', [Validators.required]],
		amountOfInsurance: new FormGroup({
			currencyUnit: new FormControl(''),
			numericalValue: new FormControl<any>('NIL', [Validators.pattern('^NIL$|^\\d+(\\.\\d{1,2})?$')]),
		}),
		// below is right side form controls
		flight: [''],
		to: [''],
		toBy2ndCarrier: [''],
		toBy3rdCarrier: [''],
		date: [''],
		byFirstCarrier: [''],
		by2ndCarrier: [''],
		by3rdCarrier: [''],
		wtOrVal: ['', [Validators.required]],
		other: ['', [Validators.required]],
		declaredValueForCarriage: new FormGroup({
			currencyUnit: new FormControl(''),
			numericalValue: new FormControl<string>('NCV', [Validators.pattern('^NCV$|^\\d+(\\.\\d{1,2})?$')]),
		}),
		declaredValueForCustoms: new FormGroup({
			currencyUnit: new FormControl(''),
			numericalValue: new FormControl<string>('NVD', [Validators.pattern('^NVD$|^\\d+(\\.\\d{1,2})?$')]),
		}),
	});

	airports: CodeName[] = [];
	filteredDepartureAirports: CodeName[] = [];
	filteredArrivalAirports: CodeName[] = [];

	constructor(
		private readonly destroyRef: DestroyRef,
		private readonly fb: NonNullableFormBuilder,
		private readonly sliCreateRequestService: SliCreateRequestService
	) {}

	ngOnInit(): void {
		this.initRefData();
		this.setupAutocomplete();
	}

	displayAirportName = (code: string): string => {
		const airport = this.airports.find((item) => item.code === code);
		return airport?.name ?? '';
	};

	private initRefData(): void {
		this.sliCreateRequestService.getAirports().subscribe((airports: CodeName[]) => {
			this.airports = airports;
			this.filteredDepartureAirports = [...airports];
		});
	}

	private setupAutocomplete(): void {
		this.airportInfoForm
			.get('departureAndRequestedRouting')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((search) => {
				this.filteredDepartureAirports = this.filterAirports(search);
			});

		this.airportInfoForm
			.get('airportOfDestination')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((search) => {
				this.filteredArrivalAirports = this.filterAirports(search);
			});
	}

	filterAirports(search: string): CodeName[] {
		return this.airports.filter((airport) => airport.code.toLowerCase().includes(search.toLowerCase().trim()));
	}
}
